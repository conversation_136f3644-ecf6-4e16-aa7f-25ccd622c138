package com.engine.dfmz4.gyl.workflow.center.web;

import com.engine.common.util.ServiceUtil;
import com.engine.dfmz4.gyl.workflow.center.service.CenterWorkService;
import com.engine.dfmz4.gyl.workflow.center.service.impl.CenterWorkServiceImpl;
import weaver.hrm.User;

/**
 * @FileName CenterWfWeb.java
 * @Description 交换中心对外接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/21
 */
public class CenterWorkWeb {
    private CenterWorkService getService(User user) {
        return ServiceUtil.getService(CenterWorkServiceImpl.class, user);
    }


}
