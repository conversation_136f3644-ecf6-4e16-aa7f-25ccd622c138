package com.engine.sd2.module.modquery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import weaver.general.Util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 建模查询工具类
 */
public class ModQueryUtil {
    private static final String HTTP_URL = "http://127.0.0.1";
    private static final String API_GETLIST = "/api/cube/search/getList";
    private static final String API_DATAS = "/api/ec/dev/table/datas";
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(ModQueryUtil.class);

    /**
     * 获取第一页数据集合
     * （可以在建模查询里设置分页数量较大的数量，这样可以查一次查完）
     *
     * @param port        当前内部服务器的端口
     * @param customid    建模查询id
     * @param cookie      cookie
     * @param queryParams 查询参数
     * @return
     */
    public static List<Map<String, Object>> getFirstDataList(String port, String customid, String cookie, Map<String, Object> queryParams) {
        List<Map<String, Object>> result = null;
        try {
            //获取sesionkey
            String dataKey = getDataKey(port, customid, cookie, queryParams);
            if (StringUtils.isNotBlank(dataKey)) {
                Map<String, Object> myParams = new HashMap<>();
                myParams.put("dataKey", dataKey);
                myParams.put("current", "1");//当前页
                log.info("getDataList myParams: " + myParams);
                //调用本机环境的接口
                String url = HTTP_URL + ":" + port + API_DATAS;
                log.info("getFirstDataList url :" + url);
                String postResult = HttpUtil.postWeaverDataWithCookie(url, cookie, myParams);

                if (StringUtils.isNotBlank(postResult)) {
                    JSONObject jsonObject = JSON.parseObject(postResult);
                    if (jsonObject != null && jsonObject.containsKey("datas")) {
                        JSONArray jsonArray = jsonObject.getJSONArray("datas");
                        if (jsonArray != null && !jsonArray.isEmpty()) {
                            log.info("getDataList result data size: " + jsonArray.size());
                            String jsonStr = jsonArray.toJSONString();
                            result = JSON.parseObject(
                                    jsonStr,
                                    new TypeReference<List<Map<String, Object>>>() {
                                    });
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("getDataList 异常", e);
        }
        return result;
    }

    /**
     * 请求data key
     *
     * @param port        当前内部服务器的端口
     * @param customid    建模查询id
     * @param cookie      cookie
     * @param queryParams 查询参数
     * @return
     */
    private static String getDataKey(String port, String customid, String cookie, Map<String, Object> queryParams) {
        String datakey = "";
        try {
            Map<String, Object> myParams = new HashMap<>();
            if (queryParams != null && !queryParams.isEmpty()) {
                myParams.putAll(queryParams);
//                myParams.put("isOnlyQuick", "1");
            }
            //额外加上固定参数
            myParams.put("customid", customid);//建模查询id
            myParams.put("guid", "search");
            myParams.put("isQuickSearch", "1");
            myParams.put("groupValue", "all");
            log.info("getDataKey myParams: " + myParams);
            String url = HTTP_URL + ":" + port + API_GETLIST;
            log.info("getDataKey url : " + url);
            String postResult = HttpUtil.postWeaverDataWithCookie(url, cookie, myParams);
            log.info("getDataKey result: " + postResult);
            if (StringUtils.isNotBlank(postResult)) {
                JSONObject jsonObject = JSON.parseObject(postResult);
                if (jsonObject != null && jsonObject.containsKey("datas")) {
                    datakey = Util.null2String(jsonObject.get("datas"));
                }
            }
        } catch (Exception e) {
            log.error("getDataKey 异常", e);
        }
        return datakey;
    }


}
