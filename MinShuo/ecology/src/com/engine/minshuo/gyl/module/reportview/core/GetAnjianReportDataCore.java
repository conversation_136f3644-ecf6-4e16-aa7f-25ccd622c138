package com.engine.minshuo.gyl.module.reportview.core;

import com.engine.minshuo.gyl.module.reportview.core.ext.GetAnjianReportDataCoreExt;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.functionlog.bean.SDLog;
import com.engine.sd2.functionlog.util.SDLogUtil;
import com.engine.sd2.module.modquery.ModQueryUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;

/**
 * @FileName GetAnjianReportDataCore.java
 * @Description 获取报告视图数据-案件报告
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/10
 */
public class GetAnjianReportDataCore extends GetAnjianReportDataCoreExt {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public GetAnjianReportDataCore(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * 初始化
     */
    private void init() {
        try {
            log.info(this.getClass().getName() + "---START---params:" + params);
            // 初始化日志打印工具类
            sdLogUtil = new SDLogUtil();
            // 初始化日志bean
            sdLog = new SDLog(user.getUID(),
                    this.getClass().getSimpleName(),
                    this.getClass().getName(),
                    SDLog.TYPE_API,
                    "获取报告数据-案件报告");
            sdLog.setRelate_module("报告");
        } catch (Exception e) {
            error = "初始化步骤异常:" + SDUtil.getExceptionDetail(e);
            log.error("初始化步骤异常", e);
        }
    }

    /**
     * 执行
     *
     * @return
     */
    public Map<String, Object> execute() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 初始化
            init();
            if (error.isEmpty()) {
                // 校验参数
                checkParam();
            }
            if (error.isEmpty()) {
                // 获取有权限的数据id列表，经过查询条件筛选后的
                getAuthIds();
                if (!authIds.isEmpty()) {
                    // 构建数据
                    buildResultData();
                } else {
                    appendLog("未查询到有权限的报告数据id");
                }

            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            appendLog(this.getClass().getName() + "---END");
            // 后置执行操作
            afterExecute();
        }
        result.put("error", error);
        result.put("status", error.isEmpty());
        result.put("data", reportData);
        log.info(this.getClass().getName() + "---END---");
        return result;
    }

    /**
     * 构建数据
     */
    private void buildResultData() {
        // 获取有权限的相关数据
        getData();
        if (reportData != null && !reportData.isEmpty()) {
            //行业-产轴
            handleField_hycz();
            //行业-精机
            handleField_hyjj();
            //涉及产品
            handleField_Sjcp();
            //跟进计划
            handleField_gjjh();
            //附件
            attachDocInfoToReportData();

            // 预分组，将详情描述数据和评论数据，分组做成map
            Map<String, List<Map<String, Object>>> detailDescMap = new HashMap<>();
            for (Map<String, Object> detail : detailInfoData) {
                String mainid = Util.null2String(detail.get("mainid"));
                detailDescMap.computeIfAbsent(mainid, k -> new ArrayList<>()).add(detail);
            }

            Map<String, List<Map<String, Object>>> commentMap = new HashMap<>();
            for (Map<String, Object> comment : commentData) {
                String rqid = Util.null2String(comment.get("rqid"));
                commentMap.computeIfAbsent(rqid, k -> new ArrayList<>()).add(comment);
            }
            // 3、循环reportData，组装描述数据和评论数据
            for (Map<String, Object> main : reportData) {
                String mainId = Util.null2String(main.get("id"));
                main.put("detailDescList", detailDescMap.getOrDefault(mainId, new ArrayList<>()));
                main.put("commentList", commentMap.getOrDefault(mainId, new ArrayList<>()));
            }
        } else {
            appendLog("筛选权限后无报告数据");
        }
    }

    /**
     * 给主数据组装文档信息
     */
    private void attachDocInfoToReportData() {
        Set<String> docIdSet = new HashSet<>();
        for (Map<String, Object> main : reportData) {
            String docids = Util.null2String(main.get("xgfj"));
            if (!docids.isEmpty()) {
                for (String id : docids.split(",")) {
                    id = id.trim();
                    if (!id.isEmpty()) {
                        docIdSet.add(id);
                    }
                }
            }
        }
        String allDocIds = String.join(",", docIdSet);
        List<DocFileInfo> docInfoList = new ArrayList<>();
        if (!allDocIds.isEmpty()) {
            docInfoList = DocUtil.getDocFileInfoByDocIds(allDocIds);
        }
        Map<String, DocFileInfo> docInfoMap = new HashMap<>();
        for (DocFileInfo doc : docInfoList) {
            docInfoMap.put(String.valueOf(doc.getDocid()), doc);
        }
        for (Map<String, Object> main : reportData) {
            String docids = Util.null2String(main.get("xgfj"));
            List<DocFileInfo> docList = new ArrayList<>();
            if (!docids.isEmpty()) {
                for (String id : docids.split(",")) {
                    id = id.trim();
                    if (!id.isEmpty() && docInfoMap.containsKey(id)) {
                        docList.add(docInfoMap.get(id));
                    }
                }
            }
            main.put("docList", docList);
        }
    }

    /**
     * 获取有权限的数据
     */
    private void getAuthIds() {
        String customid = Util.null2String(params.get("auth_customid")); // 权限建模查询id
        String cookie = Util.null2String(params.get("request_header_cookie")); // cookie
        String port = Util.null2String(params.get("port")); // 内部服务器的port端口号
        String xm = Util.null2String(params.get("xm")); // 搜索条件-项目
        String khmc = Util.null2String(params.get("khmc")); // 搜索条件-客户
        String bgr = Util.null2String(params.get("bgr")); // 搜索条件-报告人
        String startdate = Util.null2String(params.get("startdate")); // 搜索条件-开始日期
        String enddate = Util.null2String(params.get("enddate")); // 搜索条件-结束日期

        String xm_paramname = Util.null2String(params.get("xm_paramname")); // 建模搜索条件字段id-项目-对应权限建模查询的参数名
        String khmc_paramname = Util.null2String(params.get("khmc_paramname")); // 建模搜索条件字段id-客户名称-对应权限建模查询的参数名
        String bgr_paramname = Util.null2String(params.get("bgr_paramname")); // 建模搜索条件字段id-报告人-对应权限建模查询的参数名
        String bgrq_paramname = Util.null2String(params.get("bgrq_paramname")); // 建模搜索条件字段id-日期范围-对应权限建模查询的参数名

        Map<String, Object> params = new HashMap<>();
        //项目
        if (!xm.isEmpty()) {
            params.put(xm_paramname, xm);
        }
        //客户
        if (!khmc.isEmpty()) {
            params.put(khmc_paramname, khmc);
        }
        //报告人
        if (!bgr.isEmpty()) {
            params.put(bgr_paramname, bgr);
        } else {
            params.put(bgr_paramname, "-5,");
        }
        //报告日期范围
        if (!startdate.isEmpty() && !enddate.isEmpty()) {
            String dateParam = "6," + startdate + "," + enddate;
            params.put(bgrq_paramname, dateParam);
        } else {
            params.put(bgrq_paramname, "0,,");
        }
        appendLog("建模查询权限参数:" + params);
        List<Map<String, Object>> dataList = ModQueryUtil.getFirstDataList(port, customid, cookie, params);
        if (dataList != null && !dataList.isEmpty()) {
            for (Map<String, Object> map : dataList) {
                authIds.add(Util.null2String(map.get("id")));
            }
        }
    }

    /**
     * 获取相关数据
     */
    private void getData() {
        String sql;
        List<Map<String, Object>> batchData = null;
        try {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            List<String> authIdList = new ArrayList<>(authIds);
            // 确保每次查询前清空
            reportData = new ArrayList<>();
            detailInfoData = new ArrayList<>();
            commentData = new ArrayList<>();
            // 按批次获取，1000个一批次
            for (int i = 0; i < authIdList.size(); i += batchSize) {
                List<String> batch = authIdList.subList(i, Math.min(i + batchSize, authIdList.size()));
                String batchAuthIds = String.join(",", batch);
                // 1、获取主数据
                sql = getMainSql(batchAuthIds);
                appendLog("查询报告主数据sql:" + sql);
                if (rs.executeQuery(sql)) {
                    batchData = SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
                    if (!batchData.isEmpty()) {
                        reportData.addAll(batchData);
                    }
                } else {
                    error = "查询报告主数据sql出错:" + rs.getExceptionMsg();
                    appendLog(error);
                }
                //如果主数据都没有获取到，则不获取附属数据
                if (batchData != null && !batchData.isEmpty()) {
                    // 2、获取详情描述数据
                    sql = getDetailInfoSql(batchAuthIds);
                    appendLog("查询详情描述数据sql:" + sql);
                    if (rs.executeQuery(sql)) {
                        batchData = SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
                        if (!batchData.isEmpty()) {
                            detailInfoData.addAll(batchData);
                        }
                    } else {
                        error = "查询详情描述数据sql出错:" + rs.getExceptionMsg();
                        appendLog(error);
                    }

                    // 3、获取评论数据
                    sql = getDetailcommentSql(batchAuthIds);
                    appendLog("查询评论数据sql:" + sql);
                    if (rs.executeQuery(sql)) {
                        batchData = SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
                        if (!batchData.isEmpty()) {
                            commentData.addAll(batchData);
                        }
                    } else {
                        error = "查询评论数据sql出错:" + rs.getExceptionMsg();
                        appendLog(error);
                    }
                }
            }
            appendLog("报告主数据 大小：" + reportData.size());
            appendLog("详情描述数据 大小：" + detailInfoData.size());
            appendLog("评论数据 大小：" + commentData.size());

        } catch (Exception e) {
            appendLog("获取数据异常：" + SDUtil.getExceptionDetail(e));
            log.error("获取数据异常：", e);
        }

    }

    /**
     * 报告主数据sql
     *
     * @param batchAuthIds
     * @return
     */
    private String getMainSql(String batchAuthIds) {
        String fieldid_bffs = Util.null2String(params.get("fieldid_bffs")); // 拜访方式字段id
        String searchSort = Util.null2String(params.get("searchSort")); // 时间排序方式，0正序 1倒序

        // 分页参数
        String pageNum = Util.null2String(params.get("pageNum")); // 页码，从1开始
        String pageSize = Util.null2String(params.get("pageSize")); // 每页数量

        String sql = "select " +
                " a.id, " + // 数据id
                " a.bt," + // 标题
                " a.khall as kh, " + // 客户id
                " b.name as khname," + // 客户（名称）
                " b.manager as manager, " + // 客户经理id
                " c.lastname as managername," + // 客户经理名称
                " a.xycz as hycz," + // 行业-产轴id （10_1,10_2）
                " a.xyjj as hyjj," + // 行业-精机id（9_1,9_2）
                " a.tbrq as rq, " + // 报告日期
                " a.tbr as tbr," + // 报告人id
                " g.lastname as tbrname," + // 报告人姓名称
                " a.sjcp, " + // 涉及产品（多树）ids （19_7,19_17,19_18,19_13）
                " a.bffs, " + // 拜访方式id
                " f.selectname as bffsname," + // 拜访方式名称
                " a.jhgjjd as gjjh,  " + // 跟进计划（复选框）
                " a.gjjd,  " + // 跟进计划（上面选择其他时，好像会有这个多文本的值）
                " a.xgfj  " + // 相关附件 ids
                " from uf_xsbg  a " +
                " left join crm_customerinfo b on (a.khall = b.id) " +
                " left join hrmresource c on (b.manager = c.id) " +
                " left join workflow_selectitem f on (f.selectvalue = a.bffs and f.fieldid = " + fieldid_bffs + ") " +
                " left join hrmresource g on (a.tbr = g.id) " +
                " where 1=1 ";
        sql += " and a.id in (" + batchAuthIds + ") ";

        // 排序
        if ("1".equals(searchSort)) {
            sql += " order by a.tbrq desc,a.id desc ";
        } else {
            sql += " order by a.tbrq,a.id ";
        }

        // 分页处理 - SQL Server语法
        if (!pageNum.isEmpty() && !pageSize.isEmpty()) {
            try {
                int page = Integer.parseInt(pageNum);
                int size = Integer.parseInt(pageSize);
                if (page > 0 && size > 0) {
                    int offset = (page - 1) * size;
                    sql += " OFFSET " + offset + " ROWS FETCH NEXT " + size + " ROWS ONLY";
                }
            } catch (NumberFormatException e) {
                appendLog("分页参数格式错误，pageNum=" + pageNum + ", pageSize=" + pageSize);
            }
        }

        return sql;
    }


    /**
     * 详情描述数据
     *
     * @param authids
     * @return
     */
    private String getDetailInfoSql(String authids) {
        String fieldid_jd = Util.null2String(params.get("fieldid_jd")); // 阶段字段id
        String sql = "select " +
                " a.id, " + // 数据id
                " a.mainid, " + // 主表id
                " a.jd, " + // 阶段
                " b.selectname as jdname, " + // 阶段名称
                " a.xxnr " + // 详细内容
                " from uf_xsbg_dt2  a " +
                " left join workflow_selectitem b on (b.selectvalue = a.jd and b.fieldid = " + fieldid_jd + ") " +
                " where 1=1 ";
        sql += " and a.mainid in (" + authids + ") ";
        sql += "order by a.id";
        return sql;
    }

    /**
     * 详情描述数据
     *
     * @param authids
     * @return
     */
    private String getDetailcommentSql(String authids) {
        String report_modid = Util.null2String(params.get("report_modid")); // 报告的建模id
        String sql = "select " +
                " a.id, " + // 评论数据id
                " a.rqid, " + // 对应建模的数据id
                " a.replyor, " + // 评论人id
                " b.lastname as replyorname, " + // 评论人姓名
                " a.replydate, " + // 评论日期
                " a.replytime, " + // 评论时间
                " a.replycontent, " + // 评论内容 html格式
                " a.quotesid, " + // 引用评论的id
                " a.commentid, " + // 回复评论的id
                " a.commenttopid " + // 回复评论的顶级id
                " from uf_reply  a " +
                " left join hrmresource b on (b.id = a.replyor) " +
                " where 1=1 " +
                " and a.rqmodeid = " + report_modid;
        String quotedIds = "";
        if (authids != null && !authids.isEmpty()) {
            quotedIds = "'" + authids.replaceAll(",", "','") + "'";
        }
        sql += " and a.rqid in (" + quotedIds + ") "; // 建模的数据id
        sql += " order by a.rqid,a.replydate,a.replytime";
        return sql;
    }

    /**
     * 获取产品类别映射关系
     */
    private Map<String, String> getCPLBMap() {
        Map<String, String> result = new HashMap<>();
        String sql = "select * from uf_cplb ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                result.put(rs.getString("id"), rs.getString("cplb"));
            }
        }
        return result;
    }

    /**
     * 获取行业-产轴的数据 数据list
     */
    private List<Map<String, Object>> getHangyeChanZhouList() {
        String sql = "select * from ygv_Trade_cz ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
        }
        return null;
    }

    /**
     * 获取行业-精机的数据 数据list
     */
    private List<Map<String, Object>> getHangyeJingjiList() {
        String sql = "select * from ygv_Trade_jj ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
        }
        return null;
    }

    /**
     * 获取跟进计划 下拉框 数据list
     */
    private List<Map<String, Object>> getGenJinJiHuaSelect() {
        String fieldid_gjjh = Util.null2String(params.get("fieldid_gjjh"));
        String sql = "select selectvalue,selectname from workflow_selectitem where fieldid = " + fieldid_gjjh;
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
        }
        return null;
    }


    /**
     * 行业-产轴 字段处理
     */
    private void handleField_hycz() {
        //主键为fitemid，上级id为fparentid，显示名为fname，当fparentid=0时为顶级
        List<Map<String, Object>> list = getHangyeChanZhouList();
        log.info("getHangyeChanZhouList list: " + list);
        // 1. 构建id->节点的Map，方便查找
        Map<String, Map<String, Object>> idMap = new HashMap<>();
        if (list != null && !list.isEmpty()) {
            for (Map<String, Object> item : list) {
                idMap.put(Util.null2String(item.get("fitemid")), item);
            }
            for (Map<String, Object> data : reportData) {
                // 存的值：10_1
                String fieldValue = Util.null2String(data.get("hycz"));
                if (!fieldValue.isEmpty()) {
                    String id = fieldValue.trim().split("_")[1];
                    // 2. 递归查找全路径
                    List<String> path = new ArrayList<>();
                    String curId = id;
                    while (idMap.containsKey(curId)) {
                        Map<String, Object> node = idMap.get(curId);
                        path.add(0, Util.null2String(node.get("fname")));
                        String parentId = Util.null2String(node.get("fparentid"));
                        if ("0".equals(parentId) || parentId.isEmpty()) {
                            break;
                        }
                        curId = parentId;
                    }
                    data.put("hyczname", String.join("/", path));
                } else {
                    //如果字段为空，则赋值name为空
                    data.put("hyczname", "");
                }
            }
        }

    }

    /**
     * 行业-精机 字段处理
     */
    private void handleField_hyjj() {
        //主键为fitemid，上级id为fparentid，显示名为fname，当fparentid=0时为顶级
        List<Map<String, Object>> list = getHangyeJingjiList();
        log.info("handlehyjjField list: " + list);
        // 1. 构建id->节点的Map，方便查找
        Map<String, Map<String, Object>> idMap = new HashMap<>();
        if (list != null && !list.isEmpty()) {
            for (Map<String, Object> item : list) {
                idMap.put(Util.null2String(item.get("fitemid")), item);
            }
            for (Map<String, Object> data : reportData) {
                // 存的值：10_1
                String fieldValue = Util.null2String(data.get("hyjj"));
                if (!fieldValue.isEmpty()) {
                    String id = fieldValue.trim().split("_")[1];
                    // 2. 递归查找全路径
                    List<String> path = new ArrayList<>();
                    String curId = id;
                    while (idMap.containsKey(curId)) {
                        Map<String, Object> node = idMap.get(curId);
                        path.add(0, Util.null2String(node.get("fname")));
                        String parentId = Util.null2String(node.get("fparentid"));
                        if ("0".equals(parentId) || parentId.isEmpty()) {
                            break;
                        }
                        curId = parentId;
                    }
                    data.put("hyjjname", String.join("/", path));
                } else {
                    //如果字段为空，则赋值name为空
                    data.put("hyjjname", "");
                }
            }
        }
    }

    /**
     * 跟进计划 字段处理
     */
    private void handleField_gjjh() {
        List<Map<String, Object>> list = getGenJinJiHuaSelect();
        log.info("handlegjjhField list: " + list);
        // 1. 构建id->节点的Map，方便查找
        Map<String, String> map = new HashMap<>();
        if (list != null && !list.isEmpty()) {
            for (Map<String, Object> item : list) {
                map.put(Util.null2String(item.get("selectvalue")), Util.null2String(item.get("selectname")));
            }
            for (Map<String, Object> data : reportData) {
                String fieldValue = "";
                String gjjh = Util.null2String(data.get("gjjh"));
                String gjjhname = "";
                String gjjd = Util.null2String(data.get("gjjd"));
                if (!gjjh.isEmpty()) {
                    String[] gjjhArray = gjjh.split(",");
                    List<String> names = new ArrayList<>();
                    for (String id : gjjhArray) {
                        names.add(map.getOrDefault(id, ""));
                    }
                    gjjhname = String.join(",", names);
                }
                //跟进计划（复选框）
                fieldValue += gjjhname;
                //跟进进度 多文本
                if (!gjjd.isEmpty()) {
                    fieldValue += " " + gjjd;
                }
                //改成，只用gjjd字段
                data.put("gjjhname", gjjd);
            }
        }
    }


    /**
     * 涉及产品。转换
     */
    private void handleField_Sjcp() {
        //先获取产品类别的map
        Map<String, String> chanpinMap = getCPLBMap();
        log.info("chanpinMap: " + chanpinMap);
        if (!chanpinMap.isEmpty()) {
            for (Map<String, Object> data : reportData) {
                String sjcp = Util.null2String(data.get("sjcp"));
                if (!sjcp.isEmpty()) {
                    List<Map<String, Object>> sjcpList = new ArrayList<>();
                    // 假设sjcp字段是以逗号分隔的产品ID
                    String[] productIds = sjcp.split(",");
                    for (String productId : productIds) {
                        productId = productId.trim().split("_")[1];
                        if (!productId.isEmpty()) {
                            Map<String, Object> productMap = new HashMap<>();
                            productMap.put("id", productId);
                            // 从chanpinMap中获取产品类别名称
                            String categoryName = chanpinMap.get(productId);
                            productMap.put("name", categoryName != null ? categoryName : "");
                            sjcpList.add(productMap);
                        }
                    }
                    data.put("chanpinList", sjcpList);
                } else {
                    // 如果sjcp为空，设置为空列表
                    data.put("chanpinList", new ArrayList<>());
                }
            }
        }

    }

    /**
     * 后置执行操作
     */
    private void afterExecute() {
        try {
            // 清除RecordSet
            DBUtil.clearThreadLocalRecordSet();
            // 插入二开日志
            if (sdLog != null) {
                String needlog = Util.null2String(params.get("needlog")); // 是否记录日志
                if ("1".equals(needlog)) {
                    // 异步插入日志
                    SDLog.saveLogAsync(sdLog, sdLogUtil.getFullLog(), error);
                }
            }
        } catch (Exception e) {
            log.error("后置动作执行异常：", e);
        }
    }

    /**
     * 校验参数
     *
     * @return
     */
    private void checkParam() {
        List<String> missedParams = new ArrayList<>();
        String requiredParams = "port,auth_customid,report_modid,fieldid_bffs,fieldid_jd,khmc_paramname,bgr_paramname,bgrq_paramname,searchSort";
        for (String param : requiredParams.split(CommonCst.COMMA_EN)) {
            if (Util.null2String(params.get(param)).isEmpty()) {
                missedParams.add(param);
            }
        }
        if (!missedParams.isEmpty()) {
            error = "缺失必填参数:" + missedParams;
            return;
        }

        // 校验分页参数（可选参数，但如果提供了需要校验格式）
        String pageNum = Util.null2String(params.get("pageNum"));
        String pageSize = Util.null2String(params.get("pageSize"));

        if (!pageNum.isEmpty() || !pageSize.isEmpty()) {
            // 如果提供了分页参数，两个参数都必须提供
            if (pageNum.isEmpty() || pageSize.isEmpty()) {
                error = "分页参数不完整，pageNum和pageSize必须同时提供";
                return;
            }

            // 校验分页参数格式
            try {
                int page = Integer.parseInt(pageNum);
                int size = Integer.parseInt(pageSize);
                if (page <= 0) {
                    error = "页码pageNum必须大于0";
                    return;
                }
                if (size <= 0) {
                    error = "每页数量pageSize必须大于0";
                    return;
                }
                if (size > 1000) {
                    error = "每页数量pageSize不能超过1000";
                    return;
                }
                appendLog("分页参数: pageNum=" + page + ", pageSize=" + size);
            } catch (NumberFormatException e) {
                error = "分页参数格式错误，pageNum和pageSize必须为正整数";
                return;
            }
        } else {
            appendLog("未提供分页参数，将返回所有数据");
        }
    }

    /**
     * 添加日志
     *
     * @param logMsg
     */
    private void appendLog(String logMsg) {
        log.info(logMsg);
        sdLogUtil.appendLog(logMsg);
    }

}
