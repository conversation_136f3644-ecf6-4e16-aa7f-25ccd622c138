package com.engine.minshuo.gyl.module.reportview.service.impl;

import com.engine.core.impl.Service;
import com.engine.minshuo.gyl.module.reportview.core.GetXiaoshouReportDataCore;
import com.engine.minshuo.gyl.module.reportview.service.ReportViewService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;


public class ReportViewServiceImpl extends Service implements ReportViewService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> getXiaoshouReportData(Map<String, Object> params, User user) {
        return new GetXiaoshouReportDataCore(params, user).execute();
    }

    @Override
    public Map<String, Object> getAnjianReportData(Map<String, Object> params, User user) {
        return Collections.emptyMap();
    }

}
