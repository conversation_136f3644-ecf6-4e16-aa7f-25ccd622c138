package com.engine.minshuo.gyl.module.reportview.service;

import weaver.hrm.User;

import java.util.Map;


public interface ReportViewService {
    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getXiaoshouReportData(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getAnjianReportData(Map<String, Object> params, User user);
}
