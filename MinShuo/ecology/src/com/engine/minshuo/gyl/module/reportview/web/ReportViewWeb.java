package com.engine.minshuo.gyl.module.reportview.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.minshuo.gyl.module.reportview.service.ReportViewService;
import com.engine.minshuo.gyl.module.reportview.service.impl.ReportViewServiceImpl;
import com.engine.parent.common.util.ApiUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @FileName ReportViewWeb.java
 * @Description 报告自定义视图页面数据
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/10
 */
public class ReportViewWeb {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private ReportViewService getService(User user) {
        return ServiceUtil.getService(ReportViewServiceImpl.class, user);
    }

    /**
     * 获取报告视图数据
     * 销售报告
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/xiaoshou/getData")
    @Produces(MediaType.TEXT_PLAIN)
    public String getXiaoshouReportData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(getService(user).getXiaoshouReportData(params, user));
    }

    /**
     * 获取报告视图数据
     * 案件报告
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/anjian/getData")
    @Produces(MediaType.TEXT_PLAIN)
    public String getAnjianReportData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(getService(user).getAnjianReportData(params, user));
    }

}
