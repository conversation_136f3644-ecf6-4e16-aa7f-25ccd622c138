/**
 * 将时间范围转为 开始结束日期
 * @param arr 日期范围组件的值 例如 ['0']
 * @returns {{startDate: string, endDate: string}}
 */
function getDateRangeByValue(arr) {
    const type = arr[0];
    const today = new Date();
    let startDate = null, endDate = null;

    const getStartOfWeek = (date) => {
        const day = date.getDay() || 7;
        const diff = date.getDate() - day + 1;
        return new Date(date.getFullYear(), date.getMonth(), diff);
    };

    const getEndOfWeek = (date) => {
        const start = getStartOfWeek(date);
        return new Date(start.getFullYear(), start.getMonth(), start.getDate() + 6);
    };

    const getQuarter = (month) => Math.floor(month / 3);

    const getStartOfQuarter = (date) => {
        const quarter = getQuarter(date.getMonth());
        return new Date(date.getFullYear(), quarter * 3, 1);
    };

    const getEndOfQuarter = (date) => {
        const quarter = getQuarter(date.getMonth());
        return new Date(date.getFullYear(), quarter * 3 + 3, 0);
    };

    switch (type) {
        case '0': // 今年
            startDate = new Date(today.getFullYear(), 0, 1);
            endDate = new Date(today.getFullYear(), 11, 31);
            break;
        case '1': // 去年
            startDate = new Date(today.getFullYear() - 1, 0, 1);
            endDate = new Date(today.getFullYear() - 1, 11, 31);
            break;
        case '2': // 今天
            startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            break;
        case '3': // 本周
            startDate = getStartOfWeek(today);
            endDate = getEndOfWeek(today);
            break;
        case '4': // 本月
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case '5': // 本季
            startDate = getStartOfQuarter(today);
            endDate = getEndOfQuarter(today);
            break;
        case '7': // 上个月
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            startDate = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1);
            endDate = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0);
            break;
        case '6': // 指定日期范围
            if (arr.length === 3 && arr[1] && arr[2]) {
                const s = new Date(arr[1]);
                const e = new Date(arr[2]);
                if (!isNaN(s.getTime()) && !isNaN(e.getTime())) {
                    startDate = s;
                    endDate = e;
                }
            }
            break;
    }

    const format = (date) => {
        if (!date) return '';
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        return `${y}-${m}-${d}`;
    };

    return {
        startDate: format(startDate),
        endDate: format(endDate)
    };
}

const dataGroupDatas = [
    {name: '全部', value: '-1'},
    {name: '今年', value: '0'},
    {name: '去年', value: '1'},
    {name: '今天', value: '2'},
    {name: '本周', value: '3'},
    {name: '本月', value: '4'},
    {name: '本季', value: '5'},
    {name: '上个月', value: '7'},
    {name: '指定日期范围', value: '6'},

]
const defaultBrowserParams = {
    icon: "icon-toolbar-Organization-list",
    iconBgcolor: "#b32e37",
    hasAdvanceSerach: true,
    pageSize: 10,
    showCheckStrictly: true,
    selectedAllMaxLength: 500,
    displaySearchAdInBar: true,
    onBeforeFocusCheck: success => success(),
    placeholder: "请输入",
    isSingle: true,
};
const multiBrowserParams = {
    iconBgcolor: "#b32e37",
    hasAdvanceSerach: true,
    pageSize: 10,
    showCheckStrictly: true,
    selectedAllMaxLength: 500,
    displaySearchAdInBar: true,
    placeholder: "请输入",
    isSingle: false,
};

const util = {
    getDateRangeByValue,
}