const {observable, action, toJS} = mobx;
const {<PERSON><PERSON><PERSON>, <PERSON>rowserHrm} = WeaverMobilePage;

const routePath = "/app/${appId}_page"

class BasicStore {
    // 目前搜索历史存放 localStorage，需要指定固定 id
    @observable browser = new Browser.Store({route: routePath, id: 'select_kh'});
    @observable browserValue = [];
    @observable browser1 = new BrowserHrm.Store({route: routePath, id: 'select_bgr'});
    @observable browser1Value = [];

    @observable browser_manager = new BrowserHrm.Store({route: routePath, id: 'card_manager'});
    @observable browserValue_manager = [];

    @observable khmc_viewAttr = 2;

    @observable khmc = "";
    @observable reporter = "";
    @observable startdate = "";
    @observable enddate = "";
    @action
    setState = (params = {}) => {
        Object.keys(params).forEach((key) => {
            this[key] = params[key];
        });
    }
}