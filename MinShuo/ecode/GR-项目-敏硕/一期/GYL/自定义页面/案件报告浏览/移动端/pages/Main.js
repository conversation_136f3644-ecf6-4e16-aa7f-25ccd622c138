const {inject, observer} = mobxReact;
const {toJS} = mobx;
const {Form, SearchAdvanced, <PERSON><PERSON><PERSON>, BrowserHrm} = WeaverMobilePage;
const {Result, Icon, Button, SearchBar, DatePicker, List} = WeaverMobile;
const {withRouter} = ReactRouterDom;
const {DatePickerInput} = DatePicker;


const singleCustomType = "browser.SelAllCustomter";

const img2Props = {
    src: "/cloudstore/release/${appId}/resources/xiaoshou_report.png",
    height: 32,
};


@inject('basicStore')
@withRouter
@observer
class Main extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            kh_viewAttr: 2,
            fixed_khid: "",//固定客户id
            fixed_khmc: "",//固定客户名称
            fixed_manager: "",//固定客户经理id
            fixed_manager_name: "",//固定客户经理名称
            fixed_hyczname: "",//固定行业产轴
            fixed_display: "none",
            normal_display: "block",
        }
    }

    componentWillMount() {
    }

    componentDidMount() {
        const {setState} = this.props.basicStore;
        let that = this;
        //判断是否有url参数
        const {util} = window.GRSDK
        let kh = util.getBrowserUrlParam("kh");
        if (kh) {
            let khname = this.getCustomerName(kh);
            setState({
                khmc_viewAttr: 1,
                khmc: kh,
                browserValue: [{
                    id: kh,
                    name: khname,
                }]
            });
            that.refreshAll();
        } else {
            //默认执行刷新各个子组件
            this.refreshAll();
        }
    }

    getCustomerName = (kh) => {
        const {db} = window.GRSDK;
        let sql = "select name from crm_customerinfo where id =" + kh;
        let result = db.query(sql);
        if (result && result.data && result.data.length > 0) {
            return result.data[0].name;
        }
        return "";
    }

    /**
     * 设置子页面的ref，将子页面的this传递过来，方便当前页面调用
     * @param name
     * @returns {(function(*): void)|*}
     */
    setChildRef = (name) => (ref) => {
        this[name] = ref;
    }


    refreshAll = () => {
        const {khmc, reporter, startDate, endDate} = this.props.basicStore;
        console.log("refreshAll", khmc, reporter, startDate, endDate);
        if (this.content) {
            this.content.refresh(khmc || "", reporter || "", startDate || "", endDate || "");
        }
    }

    clickRefresh = () => {
        this.refreshAll();
    }


    setFixedBar = (params) => {
        this.setState({
            normal_display: "none",
            fixed_display: "block",
            fixed_khid: params.fixed_khid,
            fixed_khmc: params.fixed_khmc,
            fixed_manager: params.fixed_manager,
            fixed_manager_name: params.fixed_manager_name,
            fixed_hyczname: params.fixed_hyczname,
        })
    }

    render() {
        const {
            fixed_display, normal_display,
            fixed_khid, fixed_khmc, fixed_manager, fixed_manager_name, fixed_hyczname
        } = this.state;
        const {
            browser,
            browser1,
            setState,
            browserValue,
            browse1Value,
            startdate,
            enddate,
            khmc_viewAttr
        } = this.props.basicStore;
        return (
            <div className={"SD_Page"} id="SD_Page">
                <div className={"top-toolbar"}>
                    <div className={"title"}>
                        <img {...img2Props} />
                        <span style={{
                            fontSize: "larger",
                            paddingLeft: "10px"
                        }}>销售报告</span>
                    </div>
                    <div className={"search"}>
                        
                        <div className={"fixed_field_bar"} style={{
                            display: fixed_display
                        }}>
                            <div className="fixed_field">
                                <span className="label">客户名称</span>
                                <span className="value">{fixed_khmc}</span>
                            </div>

                            <div className="fixed_field">
                                <span className="label">客户经理</span>
                                <span className="value">{fixed_manager_name}</span>
                            </div>
                            <div className="fixed_field">
                                <span className="label">行业-产轴</span>
                                <span className="value">{fixed_hyczname}</span>
                            </div>
                        </div>

                        <div className={"searchName"} style={{
                            display: normal_display
                        }}>
                            <Browser
                                label="选择客户"
                                store={browser}
                                hasAdvanceSerach
                                type={161}
                                onChange={(...arg) => {
                                    console.log('Browser onchange: ', ...arg);
                                    setState({
                                        khmc: arg[0],
                                        browserValue: arg[2]
                                    });
                                }}
                                viewAttr={khmc_viewAttr}
                                dataParams={{
                                    type: singleCustomType,
                                }}
                                valueArray={browserValue}
                            />
                        </div>

                        <div className={"searchName"}>
                            <BrowserHrm
                                label="选择报告人"
                                store={browser1}
                                hasAdvanceSerach
                                isSingle={true}
                                type={1}
                                onChange={(...arg) => {
                                    console.log('BrowserHrm onchange: ', ...arg);
                                    setState({
                                        reporter: arg[0],
                                        browser1Value: arg[2]
                                    });
                                }}
                                valueArray={browse1Value}
                            />
                        </div>

                        <div className={"searchName"}>
                            <DatePicker
                                placeholder="开始日期"
                                mode="date"
                                value={startdate}
                                onChange={startdate => setState({startdate})}
                            >
                                <List.Item arrow="horizontal">开始日期</List.Item>
                            </DatePicker>
                        </div>
                        <div className={"searchName"}>
                            <DatePicker
                                placeholder="结束日期"
                                mode="date"
                                value={enddate}
                                onChange={enddate => setState({enddate})}
                            >
                                <List.Item arrow="horizontal">结束日期</List.Item>
                            </DatePicker>
                        </div>


                        <div className={"searchButton"}>
                            <Button type="primary"
                                    onClick={this.clickRefresh}>
                                搜索
                            </Button>
                        </div>

                    </div>
                </div>

                <Content {...this.props} setPageRef={this.setChildRef("content")} setFixedBar={this.setFixedBar}/>

            </div>

        )
    }
}