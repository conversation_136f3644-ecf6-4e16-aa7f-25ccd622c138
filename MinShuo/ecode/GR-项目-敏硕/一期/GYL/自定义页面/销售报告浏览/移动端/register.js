let enable = true;
let _this = null;

//注册组件
const WaitWeaverMobileLoad = (newProps) => {
    let WaitWeaverMobileLoadObj = ecodeSDK.getCom('${appId}', 'page');
    if (WaitWeaverMobileLoadObj) return (<WaitWeaverMobileLoadObj {...newProps} />);

    class WaitWeaverMobileLoad extends React.Component {
        constructor(props) {
            super(props);
            this.state = {
                isLoad: false
            }
            _this = this;
        }

        setIsLoad(b) {
            this.setState({
                isLoad: b
            });
        }

        render() {
            if (!this.state.isLoad) return (<div/>);
            const acParams = {
                appId: '${appId}',
                name: 'page', //模块名称
                isPage: true, //是否是路由页面
                noCss: false //是否禁止单独加载css，通常为了减少css数量，css默认前置加载
            }
            const NewCom = ecodeSDK.getAsyncCom(acParams);
            const {Route} = ReactRouterDom;
            return (
                <Route name="app" path="/app/:uuid"
                       component={NewCom}/>
            )
        }
    }

    WaitWeaverMobileLoadObj = WaitWeaverMobileLoad;
    ecodeSDK.setCom('${appId}', 'page', WaitWeaverMobileLoadObj);
    return <WaitWeaverMobileLoadObj {...newProps} />;
}

ecodeSDK.onWeaverMobileLoadQueue.push(() => {
    _this && _this.setIsLoad(true);
});

ecodeSDK.overwriteMobileClassFnQueueMapSet('FloatingTouch', {
    fn: (Com, newProps) => {
        if (!enable) return;
        const {hash, pathname} = window.location;
        if (pathname !== '/spa/custom/static4mobile/index.html') return;
        if (!hash.startsWith('#/app/${appId}_page')) return;
        if (newProps._noOverwrite) return;
        return {
            com: WaitWeaverMobileLoad,
            props: newProps
        };
    }
});

// /spa/custom/static4mobile/index.html#/app/2dcea9abdeba489c9cb986fd70cd69dc_page