body {
    /*禁止手机端双击放大页面*/
    touch-action: manipulation;
}


/*无数据组件*/
.SD_Page .nodata {
    text-align: center;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
}

.SD_Page {
    height: 100vh;
    overflow: auto;
}

.top-toolbar {

}

.SD_Page .title {
    display: flex;
    align-items: center;
    padding: 10px;
}

.SD_Page .title > span {
    margin-left: 10px;
    font-size: larger;
    font-weight: bold;
    min-width: 120px;
}

/*搜索区域*/
.SD_Page .am-list-item {
    padding-left: 20px;
}

/*搜索区域*/
.SD_Page .search .searchName {
}


.SD_Page .search .searchButton {
    margin: 10px;
}


.SD_Page .content {
    /* 确保无任何高度和滚动限制 */
    height: auto !important;
    min-height: 0 !important;
    max-height: none !important;
    overflow: visible !important;
}

.SD_Page .value a {
    color: #4d7ad8;
}

.card-list-pc {
    display: flex;
    flex-direction: column;
    gap: 28px;
}

.card-pc {
    letter-spacing: 0;
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.10);
    padding: 10px;
    font-size: 15px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: stretch;
    position: relative;
}

.card-pc .row {
    display: flex;
    margin-bottom: 4px;
}

.card-pc .row:last-child {
    margin-bottom: 0;
}

.card-pc .label {
    color: #888;
    min-width: 70px;
    font-weight: 500;
    margin-right: 4px;
    font-size: 14px;
    line-height: 1.4;
}

.card-pc .value {
    color: #222;
    flex: 1;
    font-size: 14px;
    line-height: 1.5;
    border-bottom: 1px solid #aaa;
}

.card-pc .attach {
    /*max-height: 80px;*/
    overflow-y: auto;
    overflow-x: hidden;
    word-break: break-all;
}


.card-pc .attach-row .value {
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
}

.list-end {
    text-align: center;
    color: #aaa;
    padding: 16px 0 8px 0;
    font-size: 14px;
    min-height: 100px;
    /* 新增，保证有下拉空间 */
    margin-bottom: 60px;
    /* 新增，增加底部空间 */
    user-select: none;
    /* 新增，防止选中 */
}

.card-pc-flex {
    display: flex;
    flex-direction: row;
    gap: 32px;
    margin: 10px;
}

.card-pc-left {
    flex: 6;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: stretch;
    gap: 8px;
    margin-top: 30px;
}

.card-pc-right .row.value-area.scrollable,
.card-pc-left .row.value-area.scrollable {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    overflow-y: auto;
    background: #f7f7f7;
    border-radius: 8px;
    padding: 6px 8px;
    margin-bottom: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.comment-item {
    width: 100%;
    margin-bottom: 6px;
    border-bottom: 1px solid #ececec;
    padding-bottom: 4px;
    word-break: break-all;
}

.comment-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.comment-meta {
    font-size: 13px;
    color: #888;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
}

.comment-date {
    margin-left: 10px;
    color: #bbb;
    font-size: 12px;
}

.comment-content {
    font-size: 14px;
    color: #222;
    white-space: pre-line;
    line-height: 1.6;
}

.row.align-field {
    display: flex;
    align-items: flex-start;
    margin-bottom: 4px;
}

.row.align-field .label {
    line-height: 1.4;
    padding-top: 4px;
}

.row.align-field .value-area {
    flex: 1;
    margin: 0;
}


.value-area.scrollable.high-desc {
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    background: #f7f7f7;
    border-radius: 8px;
    padding: 6px 8px;
    box-sizing: border-box;
    overflow: auto;
}

.value-area.scrollable.high-plan {
    /*height: 60px;*/
    overflow: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    background: #f7f7f7;
    border-radius: 8px;
    padding: 6px 8px;
    box-sizing: border-box;
    font-size: 14px;
    color: #222;
}

.comment-full-height {
    max-height: 150px;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

}

.card-pc-right {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.card-pc-right .row.value-area.scrollable.comment-full-height {
    flex: 1 1 auto;
    height: auto;
    min-height: 180px;
}

.desc-jdname {
    font-weight: bold;
    font-size: 14px;
    color: #222;
}

.desc-xxnr {
    font-size: 13px;
    color: #666;
    white-space: pre-line;
    text-indent: 1em;
}

.card-loading {
    text-align: center;
    margin-top: 150px;
}


/* 卡片左上角序号样式 */
.card-index {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 18px;
    height: 18px;
    background: #eee;
    /* color: #fff; */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
    z-index: 2;
    user-select: none;
    letter-spacing: 0;
}

.fixed_field_bar {
    background: white;
    padding-left: 20px;
    padding-bottom: 10px;
}

.fixed_field_bar .fixed_field {
    display: flex;
    align-items: center;
    padding-right: 20px;
    padding-top: 20px;
}

.fixed_field_bar .fixed_field .label {
    min-width: 80px;
    margin-right: 4px;
    line-height: 1.4;
}
