const img2PropsNoData = {
    src: "/cloudstore/release/${appId}/resources/nodata.png",
    height: 100,
};

class NoData extends React.Component {
    constructor(props) {
        super(props);
        let currentYear = new Date().getFullYear() + "";
        this.state = {}
    }

    componentWillMount() {
    }

    componentDidMount() {

    }


    render() {
        return (
            <div className={"nodata"}>
                <div>
                    <img {...img2PropsNoData} />
                    <div>暂无数据</div>
                </div>
            </div>

        )
    }
}