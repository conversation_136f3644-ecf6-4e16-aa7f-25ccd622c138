const {Provider} = mobxReact;
const Main = ecodeSDK.imp(Main);
//实例化store，并通过provider注入所有组件中
const allSimpleStore = {
    basicStore: new BasicStore()
}

class simpleRoot extends React.Component {
    render() {
        return (
            <Provider {...allSimpleStore}>
                <Main {...this.props} />
            </Provider>
        )
    }
}

//发布模块
ecodeSDK.setCom('${appId}', 'page', simpleRoot);