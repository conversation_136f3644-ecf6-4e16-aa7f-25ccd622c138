const {WeaDateGroup, Wea<PERSON><PERSON><PERSON>, <PERSON>aLocaleProvider, WeaSlideModal} = ecCom
const {Spin} = antd

const slide_report = (
    <iframe id="iframe_report" width="100%" height="100%" style={{
        height: "100vh",
        paddingTop: "10px",
        paddingBottom: "10%"
    }}></iframe>
)

//读取config
const config = ecodeSDK.getCom("${appId}", "config");


class Content extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            option: {},
            hasData: true, // 默认有数据
            data: [], // 数据列表
            page: 1,
            pageSize: 10,
            loading: false, //接口读取数据的loading
            finished: false,
            allData: [],
            canLoadMore: false, // 新增：只有下拉到提示后再继续下拉才加载
            fixed_khid: "",//固定客户id
            fixed_khmc: "",//固定客户名称
            fixed_manager: "",//固定客户经理id
            fixed_manager_name: "",//固定客户经理名称
            fixed_hyczname: "",//固定行业产轴
            fixed_display: "none",
            visible_report: false,
            normal_display: "flex"
        }
        this.scrollRef = React.createRef();
        this.listEndRef = React.createRef();
    }

    componentWillMount() {

    }

    componentDidMount() {
        this.props.setPageRef(this)
        const container = document.getElementById('container');
        if (container) {
            container.addEventListener('scroll', this.handleScroll);
        }
        // 页面初始时主动判断一次
        setTimeout(() => {
            this.handleScroll();
        }, 0);
    }

    componentWillUnmount() {
        const container = document.getElementById('container');
        if (container) {
            container.removeEventListener('scroll', this.handleScroll);
        }
    }

    /**
     * 刷新
     * @param khmc
     * @param reporter
     * @param startdate
     * @param enddate
     * @param searchSort
     */
    refresh = (khmc, reporter, startdate, enddate, searchSort) => {
        let that = this;
        this.setState({
            khmc: khmc,
            reporter: reporter,
            startdate: startdate,
            enddate: enddate,
            searchSort: searchSort
        }, () => {
            that.doRefresh()
        })
    }

    doRefresh = () => {
        let that = this;
        const {khmc, reporter, startdate, enddate, pageSize, searchSort} = this.state;
        //使用接口获取数据
        const {http, util} = window.GRSDK
        let params = {
            port: config.port,//当前服务器内网环境端口号（后端调用建模查询接口时使用）
            auth_customid: config.auth_customid,//权限建模查询id
            khmc_paramname: config.khmc_paramname,//权限查询条件字段id-客户
            bgr_paramname: config.bgr_paramname,//权限查询条件字段id-报告人
            bgrq_paramname: config.bgrq_paramname,//权限查询条件字段id-报告日期
            report_modid: config.report_modid,//报告建模id
            fieldid_bffs: config.fieldid_bffs,//主表字段id-拜访方式（查询下拉框值需要用到）
            fieldid_jd: config.fieldid_jd, //明细2字段id-阶段（查询下拉框值需要用到）
            fieldid_gjjh: config.fieldid_gjjh,//主表字段id-跟进计划（查询下拉框值需要用到）
            khmc: khmc, //查询条件-客户
            bgr: reporter, //查询条件-报告人
            startdate: startdate, //查询条件-开始日期
            enddate: enddate, //查询条件-结束日期
            searchSort: searchSort//查询时间顺序 0正序 1倒序
        }
        this.setState({
            loading: true
        })

        let list_gap_height = "28px";
        let normal_display = "flex";
        let kh = util.getBrowserUrlParam("kh");
        http.postAC(config.api_url, params, (result) => {
            console.log("获取销售报告数据result", result);
            let data = [];
            if (result && result.status === true) {
                data = result.data;
            }

            if (kh && data.length > 0) {
                list_gap_height = "68px";
                normal_display = "none";
                let params = {
                    fixed_khid: data[0].kh, //客户
                    fixed_khmc: data[0].khname,
                    fixed_manager: data[0].manager,
                    fixed_manager_name: data[0].managername,//经理
                    fixed_hyczname: data[0].hyczname,//行业产轴
                }
                that.props.setFixedBar(params);
            }
            that.setState({
                loading: false,
                normal_display: normal_display,
                list_gap_height: list_gap_height,
                allData: data,
                data: data.slice(0, pageSize),
                finished: data.length <= pageSize
            })
        });
    }


    handleScroll = () => {
        if (this.state.loading || this.state.finished || this.state.loadingmore) return;
        const container = document.getElementById('container');
        if (!container) return;
        const scrollTop = container.scrollTop;
        const clientHeight = container.clientHeight;
        const scrollHeight = container.scrollHeight;

        // 距离底部小于100px时自动加载
        if (scrollHeight - (scrollTop + clientHeight) < 200) {
            this.loadMore();
        }
    }

    loadMore = () => {
        if (this.state.loading || this.state.finished || this.state.loadingmore) return;
        this.setState({
            loadingmore: true,
        })
        setTimeout(() => {
            const {allData, data, page, pageSize} = this.state;
            const nextPage = page + 1;
            const nextData = allData.slice(0, nextPage * pageSize);
            this.setState({
                loadingmore: false,
                data: nextData,
                page: nextPage,
                finished: nextData.length >= allData.length
            });
        }, 300);
    }

    // 处理评论内容，确保 <br> 标签能正确显示
    processCommentContent = (content) => {
        if (!content) return '';

        // 处理可能被编码的 <br> 标签和全角字符
        let processedContent = content
            .replace(/&lt;br&gt;/gi, '<br>')
            .replace(/&lt;br\/&gt;/gi, '<br>')
            .replace(/&lt;br \/&gt;/gi, '<br>')
            .replace(/＜br＞/gi, '<br>')  // 全角尖括号
            .replace(/＜br\/＞/gi, '<br>')  // 全角尖括号
            .replace(/＜br \/＞/gi, '<br>')  // 全角尖括号
            .replace(/\n/g, '<br>'); // 将换行符也转换为 <br>

        return processedContent;
    }

    renderCard = (item, idx) => {
        const {normal_display} = this.state;
        if (idx === 1) {
            console.log("1 item", item)
        }
        let gjjhname = item.gjjhname ? item.gjjhname : "(无)";

        // PC端布局，左右分栏，右侧为评论
        return (
            <div className="card-pc card-pc-flex" key={item.id}>
                <div className="card-index" title={item.id}>{idx + 1}</div>
                <div className="card-pc-left">
                    {/* 左侧第一行 */}
                    <div className="row" style={{
                        display: normal_display
                    }}>
                        <span className="label">客户名称</span>
                        <span className="value">
                                <a
                                    href={`/spa/crm/static/index.html#/main/crm/customerView?customerId=${item.kh}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    {item.khname}
                                </a>
                            </span>
                        <span className="label">客户经理</span>
                        <span className="value">
                                <WeaBrowser
                                    type={1}
                                    title="人力资源"
                                    showDls
                                    viewAttr={1}
                                    replaceDatas={[{id: item.manager, name: item.managername}]}
                                    linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                    {...defaultBrowserParams}
                                />

                            </span>
                    </div>
                    {/* 行 */}
                    <div className="row"><span className="label">报告日期</span>
                        <span className="value">
                                <a
                                    href="#"
                                    onClick={e => {
                                        e.preventDefault();
                                        this.openSlide(item.id);
                                    }}
                                >
                                    {item.rq}
                                </a>
                            </span>
                        <span className="label">报告人</span>
                        <span className="value">
                                <WeaBrowser
                                    type={1}
                                    title="人力资源"
                                    showDls
                                    viewAttr={1}
                                    replaceDatas={[{id: item.tbr, name: item.tbrname}]}
                                    linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                    {...defaultBrowserParams}
                                />
                            </span>
                    </div>
                    <div className="row"><span className="label">标题</span>
                        <span className="value">
                                <a
                                    href="#"
                                    onClick={e => {
                                        e.preventDefault();
                                        this.openSlide(item.id);
                                    }}
                                >
                                    {item.bt}
                                </a>
                            </span>
                    </div>
                    {/* 行 */}
                    <div className="row" style={{
                        display: normal_display
                    }}>
                        <span className="label">行业-产轴</span>
                        <span className="value">{item.hyczname}</span>
                    </div>
                    {/*<div className="row"><span className="label">行业-精机</span><span*/}
                    {/*    className="value">{item.hyjjname}</span>*/}
                    {/*</div>*/}
                    {/* 行 */}
                    <div className="row">
                        <span className="label">涉及产品</span>
                        <span className="value" style={{
                            // maxHeight: "80px",
                            overflow: "auto"
                        }}>
                                <WeaBrowser
                                    type={257}
                                    title="自定义树形多选"
                                    dataParams={{cube_treeid: "31_searchType_searchType_searchType"}}
                                    destDataParams={{
                                        browsertype: "",
                                        cube_treeid: 31,
                                        isshowsearchtab: 0,
                                        searchbrowserid: 0
                                    }}
                                    asynLoadAll
                                    defaultExpandedLevel={0}
                                    hasAdvanceSerach={false}
                                    isMultCheckbox
                                    viewAttr={1}
                                    linkUrl="/spa/cube/index.html#/main/cube/card?type=0&modeId=2&billid="
                                    replaceDatas={item.chanpinList}
                                    {...multiBrowserParams}
                                />
                            </span>
                    </div>
                    {/* 行 */}
                    <div className="row">
                        <span className="label">拜访方式</span><span
                        className="value">{item.bffsname}</span>
                    </div>
                    {/* 行 */}
                    <div className="row align-field">
                        <span className="label">详情描述</span>
                        <span className="value-area scrollable top-align high-desc">
                                {Array.isArray(item.detailDescList) && item.detailDescList.length > 0
                                    ? item.detailDescList.map((d, idx) => (
                                        <div key={d.id} className="desc-item">
                                            <div className="desc-jdname">{idx + 1}. {d.jdname}</div>
                                            <div className="desc-xxnr">{d.xxnr}</div>
                                        </div>
                                    ))
                                    : "(无)"}
                            </span>
                    </div>
                    {/* 行 */}
                    <div className="row align-field"><span className="label">跟进计划</span><span
                        className="value-area scrollable top-align high-plan">{gjjhname}</span>
                    </div>
                    {/* 行 */}
                    <div className="row">
                        <span className="label">附件</span>
                        <span className="value" style={{
                            // maxHeight: "80px",
                            // overflow: "auto"
                            wordBreak: "break-all",
                        }}>
                            {item.docList.map((c, i) => (
                                <a style={{marginLeft: "5px"}}
                                   href="#"
                                   onClick={e => {
                                       e.preventDefault();
                                       this.clickAttach(c, item);
                                   }}
                                >
                                    {c.fileName}
                                </a>
                            ))}
                            </span>
                    </div>
                </div>
                <div className="card-pc-right">
                    <div className="row"><span className="label">评论</span></div>
                    <div className="row value-area scrollable top-align comment-full-height">
                        {item.commentList && item.commentList.length > 0 ? item.commentList.map((c, i) => (
                            <div className="comment-item" key={i}>
                                <div className="comment-meta">
                                    <WeaBrowser
                                        type={1}
                                        title="人力资源"
                                        showDls
                                        viewAttr={1}
                                        replaceDatas={[{id: c.replyor, name: c.replyorname}]}
                                        linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                        inputStyle={{width: 'auto'}}
                                        {...defaultBrowserParams}
                                    />
                                    <span className="comment-date">{c.replydate} {c.replytime}</span>
                                </div>
                                <div className="comment-content"
                                     dangerouslySetInnerHTML={{__html: this.processCommentContent(c.replycontent)}}></div>
                            </div>
                        )) : "(无)"}

                    </div>
                </div>
            </div>
        )

    }

    /**
     * 侧滑打开建模卡片
     * @param attach
     * @param item
     */
    clickAttach = (attach, item) => {
        console.log("clickAttach", attach, item);
        let moduleid = config.report_modid;
        let authorizeformmodeFieldId = config.authorizeformmodeFieldId;
        let currentUser = ecodeSDK.getEcodeParams(['ecode_params'])._user.id;
        let url = "/spa/document/index2file.jsp?f_weaver_belongto_userid=" + currentUser + "&f_weaver_belongto_usertype=0&id=" + attach.docid + "&formmode_authorize=formmode_authorize&moduleid=formmode&authorizemodeId=" + moduleid + "&authorizefieldid=" + authorizeformmodeFieldId + "&authorizeformmodebillId=" + item.id + "&imagefileId=" + attach.fileid + "&isFromAccessory=true&router=1#/main/document/fileView"
        window.open(url);
    }
    /**
     * 侧滑打开建模卡片
     * @param billid
     */
    openSlide = (billid) => {
        let moduleid = config.report_modid;
        let url = "/spa/cube/index.html#/main/cube/card?type=0&modeId=" + moduleid + "&billid=" + billid;
        $("#iframe_report").attr('src', url)
        this.setState({
            visible_report: true
        })
    }

    onCloseSlide = () => {
        this.setState({visible_report: false})
        console.log('close')
        $("#iframe_report").attr('src', '');

    }


    render() {
        const {
            hasData, data, finished, allData, visible_report, loading, list_gap_height
        } = this.state;
        let showLoadMore = data.length < (allData ? allData.length : 0);

        let content = (
            <div className={"content"}>

                <div className={"card-list-pc"} style={{
                    gap: list_gap_height
                }} ref={this.scrollRef}>
                    {visible_report && (
                        <div
                            style={{
                                position: 'fixed',
                                top: 0, left: 0, right: 0, bottom: 0,
                                zIndex: 10,
                                background: 'rgba(0,0,0,0.01)',
                            }}
                            onClick={this.onCloseSlide}
                        />
                    )}
                    <WeaSlideModal visible={visible_report}
                                   top={10}
                                   width={80}
                                   height={100}
                                   direction={'right'}
                                   measure={'%'}
                                   title={''}
                                   content={slide_report}
                                   closeMaskOnClick={true}
                                   onClose={this.onCloseSlide}
                                   hasScroll={false}
                    />

                    {data.map(this.renderCard)}
                    {showLoadMore && <div className="list-end" ref={this.listEndRef}>继续下拉显示更多↓</div>}
                    {!showLoadMore && finished && <div className="list-end">-----已经到底了-----</div>}
                </div>
            </div>
        )
        let loadingdiv = (
            <div className="card-loading">
                < Spin spinning={true} tip="正在读取数据..."/>
            </div>
        )
        let finalContent = hasData === true ? content : <NoData/>
        if (loading) {
            finalContent = loadingdiv;
        }
        console.log("finalContent", finalContent)
        return (
            <div>{finalContent}</div>

        )
    }
}
