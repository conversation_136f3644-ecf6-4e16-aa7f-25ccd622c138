//设置config
const config = {
    port: "80",//当前服务器内网环境端口号（后端调用建模查询接口时使用）
    auth_customid: "251",//权限建模查询id
    khmc_paramname: "7624",//权限查询条件字段id-客户
    bgr_paramname: "6587",//权限查询条件字段id-报告人
    bgrq_paramname: "6589",//权限查询条件字段id-报告日期
    report_modid: "45",//报告建模id
    fieldid_bffs: "6599",//主表字段id-拜访方式（查询下拉框值需要用到）
    fieldid_jd: "6608", //明细2字段id-阶段（查询下拉框值需要用到）
    fieldid_gjjh: "6602",//主表字段id-跟进计划（查询下拉框值需要用到）
    api_url: "/api/sd/reportview/xiaoshou/getData",//二开接口地址，获取销售报告数据
    authorizeformmodeFieldId: 6603, //建模-销售报告-相关附件字段id
};
ecodeSDK.setCom("${appId}", "config", config);